{"name": "slim/slim-skeleton", "description": "A Slim Framework skeleton application for rapid development", "keywords": ["microframework", "rest", "router", "psr7"], "homepage": "http://github.com/slimphp/Slim-Skeleton", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.joshlockhart.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.lgse.com/"}], "require": {"php": "^8.2", "ext-json": "*", "guzzlehttp/guzzle": "^7.9", "illuminate/database": "^11.31", "illuminate/support": "^11.31", "monolog/monolog": "^2.8", "mustache/mustache": "^2.14", "php-di/php-di": "^6.4", "phpmailer/phpmailer": "^6.10", "slim/psr7": "^1.5", "slim/slim": "^4.10"}, "require-dev": {"jangregor/phpstan-prophecy": "^1.0.0", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.26", "squizlabs/php_codesniffer": "^3.7"}, "config": {"allow-plugins": {"phpstan/extension-installer": true}, "process-timeout": 0, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}}