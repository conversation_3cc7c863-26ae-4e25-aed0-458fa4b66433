<?php

declare(strict_types=1);

use App\Application\Services\EmailService;
use App\Application\Settings\SettingsInterface;
use DI\ContainerBuilder;
use GuzzleHttp\Client;
use Illuminate\Database\Capsule\Manager as Capsule;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Monolog\Processor\UidProcessor;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

return function (ContainerBuilder $containerBuilder) {
    $containerBuilder->addDefinitions([

        /**
         * Logger con colores en entornos no productivos
         */
        LoggerInterface::class => function (ContainerInterface $c) {
            /** @var SettingsInterface $settings */
            $settings = $c->get(SettingsInterface::class);
            $loggerSettings = $settings->get('logger');

            // Crear instancia del Logger
            $logger = new Logger($loggerSettings['name']);

            // Añadir un processor para UID (opcional)
            $processor = new UidProcessor();
            $logger->pushProcessor($processor);

            // Crear un handler para la salida
            $stream = new StreamHandler($loggerSettings['path'], $loggerSettings['level']);

            // Obtener el entorno de la variable de entorno
            $appEnv = getenv('APP_ENV') ?: 'production';

            if ($appEnv !== 'production') {
                // Definir códigos de colores ANSI para diferentes niveles
                $colors = [
                    Logger::DEBUG => "\033[37m", // Blanco
                    Logger::INFO => "\033[32m", // Verde
                    Logger::NOTICE => "\033[36m", // Cian
                    Logger::WARNING => "\033[33m", // Amarillo
                    Logger::ERROR => "\033[31m", // Rojo
                    Logger::CRITICAL => "\033[35m", // Magenta
                    Logger::ALERT => "\033[41m", // Fondo Rojo
                    Logger::EMERGENCY => "\033[41m", // Fondo Rojo
                ];

                $reset = "\033[0m"; // Código para resetear el color

                // Formato de línea personalizado
                $format = "%datetime% > %level_name% > %message% %context% %extra%\n";

                // Crear el LineFormatter
                $formatter = new LineFormatter($format, null, true, true);
                $formatter->setMaxNormalizeDepth(50);
                $stream->setFormatter($formatter);

                // Añadir un processor para aplicar colores
                $logger->pushProcessor(function ($record) use (&$colors, &$reset) {
                    if (isset($colors[$record['level']])) {
                        // Aplicar color al nivel
                        $record['level_name'] = $colors[$record['level']] . $record['level_name'] . $reset;
                        // Aplicar color al mensaje
                        $record['message'] = $colors[$record['level']] . $record['message'] . $reset;
                    }
                    return $record;
                });
            } else {
                // En producción, usar el LineFormatter estándar sin colores
                $formatter = new LineFormatter(null, null, true, true);
                $stream->setFormatter($formatter);
            }

            // Añadir el handler al logger
            $logger->pushHandler($stream);

            return $logger;
        },

        /**
         * Eloquent
         */
        Capsule::class => function (ContainerInterface $c) {
            $settings = $c->get(SettingsInterface::class);
            $capsule = new Capsule();
            $capsule->addConnection(
                $settings->get('sistemainmobiliario')
            ); // 'default' -> sistemainmobiliario
            $capsule->addConnection(
                $settings->get('publiweb'),
                'publiweb'
            ); // 'publiweb' -> publiweb
            $capsule->setAsGlobal();
            $capsule->bootEloquent();
            return $capsule;
        },

        /**
         * Guzzle/Http/Client
         */
        Client::class => function () {
            return new Client();
        },

        /**
         * Email Service
         */
        EmailService::class => function (ContainerInterface $c) {
            return new EmailService(
                $c->get(SettingsInterface::class),
                $c->get(LoggerInterface::class)
            );
        },
    ]);
};
