# Funcionalidad de Solicitud de Socios

## Descripción
Esta funcionalidad permite a los owners solicitar ser socios de otros profesionales inmobiliarios, enviando automáticamente notificaciones por email tanto al destinatario como al solicitante.

## Archivos Implementados

### 1. Template de Solicitud
- **Archivo**: `templates/solicitud-socio.html`
- **Descripción**: Template HTML para el email que recibe el destinatario
- **Características**:
  - Diseño profesional y responsive
  - Información completa del solicitante
  - Botones de acción (Aceptar/Rechazar/Ver Perfil)
  - Lista de beneficios de la sociedad
  - Información de contacto

### 2. Template de Confirmación
- **Archivo**: `templates/confirmacion-solicitud-socio.html`
- **Descripción**: Template HTML para la confirmación al solicitante
- **Características**:
  - Confirmación visual de envío exitoso
  - Detalles de la solicitud enviada
  - Estado actual y próximos pasos
  - Información de contacto del destinatario

### 3. EmailService Extendido
- **Archivo**: `src/Application/Services/EmailService.php`
- **Métodos agregados**:
  - `enviarSolicitudSocio()`: Envía solicitud y confirmación
  - `sendSolicitudEmailToDestination()`: Email al destinatario
  - `sendSolicitudConfirmationToSender()`: Confirmación al solicitante
  - `prepareSolicitudTemplateData()`: Prepara datos para templates
  - `generateSolicitudTextContent()`: Versión texto del email
  - `generateSolicitudConfirmationTextContent()`: Versión texto de confirmación

### 4. Acción Actualizada
- **Archivo**: `src/Application/Actions/Owner/Socios/SolicitarSocioOwnerAction.php`
- **Funcionalidades agregadas**:
  - Obtención de información del solicitante y destinatario
  - Envío automático de emails después de crear la solicitud
  - Logging detallado de eventos
  - Manejo de errores robusto
  - Respuestas informativas sobre el estado del envío

## Flujo de Funcionamiento

### 1. **Solicitud Iniciada**:
- Owner hace POST a `/owner/socios/{id}/solicitar`
- Se valida que no exista ya una relación de socios
- Se obtiene información del solicitante y destinatario

### 2. **Creación en Base de Datos**:
- Se crea registro en tabla `bolsa_inmobiliaria`
- Estado inicial: `authorized_at` = NULL (pendiente)

### 3. **Envío de Emails**:
- **Email al destinatario**: Notificación de nueva solicitud
- **Email al solicitante**: Confirmación de envío

### 4. **Respuesta del Sistema**:
- Confirmación de solicitud creada
- Estado del envío de emails
- IDs de seguimiento

## Datos Procesados

### Información del Solicitante:
- Nombre y empresa
- Email y teléfono
- Ubicación (ciudad, estado)
- ID de contrato

### Información del Destinatario:
- Nombre y empresa
- Email y teléfono
- Ubicación (ciudad, estado)
- ID de contrato

## Templates de Email

### Email al Destinatario (`solicitud-socio.html`):
```html
📋 Nueva Solicitud de Sociedad

Información del Solicitante:
- Nombre: [nombre]
- Empresa: [empresa]
- Email: [email]
- Teléfono: [teléfono]
- Ubicación: [ciudad, estado]

Acciones:
[Aceptar Solicitud] [Rechazar Solicitud] [Ver Perfil]
```

### Confirmación al Solicitante (`confirmacion-solicitud-socio.html`):
```html
✅ Solicitud Enviada Exitosamente

Detalles de tu Solicitud:
- Destinatario: [nombre]
- Empresa: [empresa]
- Estado: Pendiente de respuesta

Próximos Pasos:
1. El destinatario recibirá tu solicitud
2. Podrá revisar tu perfil
3. Tomará una decisión
4. Recibirás notificación de su respuesta
```

## Enlaces de Acción

Los emails incluyen enlaces directos para:
- **Aceptar**: `/owner/socios/{id}/autorizar`
- **Rechazar**: `/owner/socios/{id}/rechazar`
- **Ver Perfil**: `/owner/socios/perfil/{id}`

## Respuestas del API

### Éxito con emails enviados:
```json
{
  "statusCode": 200,
  "data": {
    "contrato_id": 123,
    "message": "Solicitud creada y notificaciones enviadas",
    "solicitud_id": 456,
    "emails_sent": true
  }
}
```

### Éxito con error en emails:
```json
{
  "statusCode": 200,
  "data": {
    "contrato_id": 123,
    "message": "Solicitud creada (error al enviar notificaciones)",
    "solicitud_id": 456,
    "emails_sent": false,
    "email_error": "Error específico"
  }
}
```

### Error - Ya son socios:
```json
{
  "statusCode": 400,
  "data": {
    "message": "Ya son socios directos"
  }
}
```

## Logging

El sistema registra:
- **Info**: Solicitud creada y emails enviados exitosamente
- **Warning**: Solicitud creada pero error en emails
- **Error**: Errores de validación, base de datos o configuración
- **Debug**: Detalles del envío de cada email

## Configuración Requerida

Misma configuración SMTP de Brevo que las invitaciones:
```bash
BREVO_SMTP_SERVER=smtp-relay.brevo.com
BREVO_SMTP_PORT=587
BREVO_SMTP_USERNAME=<EMAIL>
BREVO_SMTP_PASSWORD=tu-password-smtp
BREVO_FROM_EMAIL=<EMAIL>
BREVO_FROM_NAME=Multibolsa Inmobiliaria
```

## Ventajas de esta Implementación

- ✅ **Notificación inmediata**: El destinatario sabe al instante de la solicitud
- ✅ **Confirmación al solicitante**: Tranquilidad de que se envió correctamente
- ✅ **Enlaces directos**: Acciones rápidas desde el email
- ✅ **Información completa**: Todos los datos necesarios para tomar decisión
- ✅ **Fallback robusto**: La solicitud se crea aunque fallen los emails
- ✅ **Logging detallado**: Tracking completo para debugging
- ✅ **Templates profesionales**: Imagen corporativa consistente

## Casos de Uso

1. **Networking profesional**: Conectar con otros agentes inmobiliarios
2. **Expansión de red**: Ampliar alcance geográfico o de mercado
3. **Colaboraciones**: Establecer partnerships para proyectos específicos
4. **Referidos**: Crear red de referidos mutuos

## Testing

Para probar la funcionalidad:
1. Configurar credenciales SMTP de Brevo
2. Hacer POST a `/owner/socios/{id}/solicitar`
3. Verificar que se cree el registro en `bolsa_inmobiliaria`
4. Confirmar que lleguen ambos emails
5. Revisar logs para tracking completo

## Próximas Mejoras

1. **Notificaciones push**: Complementar emails con notificaciones en app
2. **Templates personalizables**: Permitir personalizar mensajes
3. **Recordatorios**: Reenvío automático si no hay respuesta
4. **Analytics**: Métricas de aceptación/rechazo
5. **Integración con CRM**: Sincronización con sistemas externos
