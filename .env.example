# Configuración de la aplicación
APP_ENV=development
APP_DOMAIN=msi-v5

# Base de datos Sistema Inmobiliario
SI_MYSQL_SERVER=host.docker.internal
SI_MYSQL_USERNAME=root
SI_MYSQL_PASSWORD=password
SI_MYSQL_DATABASE=sistemainmobiliario

# Base de datos Publiweb
PW_MYSQL_SERVER=host.docker.internal
PW_MYSQL_DATABASE=publiweb
PW_MYSQL_USERNAME=root
PW_MYSQL_PASSWORD=password

# Configuración de Brevo SMTP (para envío de emails)
BREVO_SMTP_SERVER=smtp-relay.brevo.com
BREVO_SMTP_PORT=587
BREVO_SMTP_USERNAME=<EMAIL>
BREVO_SMTP_PASSWORD=xsmtpsib-...
BREVO_FROM_EMAIL=<EMAIL>
BREVO_FROM_NAME=Multibolsa Inmobiliaria

# Microservicios
MSO_INMUEBLES=http://localhost:8080
