<?php

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Infrastructure\Models\SIInvitation;
use App\Infrastructure\Models\SISociosDirectos;
use Carbon\Carbon;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpBadRequestException;
use Slim\Exception\HttpNotFoundException;

class AceptarInvitacionOwnerAction extends OwnerAction
{
    protected function action(): Response
    {
        // Obtener token del query string o del body
        $token = $this->request->getQueryParams()['token'] ??
            $this->getFormData()['token'] ?? null;

        if (!$token) {
            throw new HttpBadRequestException(
                $this->request,
                'Token de invitación requerido'
            );
        }

        // Buscar la invitación
        $invitation = SIInvitation::findByToken($token);

        if (!$invitation) {
            throw new HttpNotFoundException(
                $this->request,
                'Invitación no encontrada'
            );
        }

        // Verificar que la invitación esté pendiente
        if (!$invitation->isPending()) {
            return $this->respondWithData([
                'success' => false,
                'message' => 'Esta invitación ya ha sido procesada',
                'status' => $invitation->status_name
            ], 400);
        }

        try {
            // Crear la relación de socios
            $sociosDirectos = new SISociosDirectos([
                'contrato_solicitante' => $invitation->contrato_id,
                'contrato_solicitado' => $this->auth->getContratoId(),
                'authorized_at' => Carbon::now()
            ]);
            $sociosDirectos->save();

            // Marcar la invitación como aceptada
            $invitation->markAsAccepted();
            $invitation->addToBitacora([
                'action' => 'accepted_by_contrato',
                'contrato_id' => $this->auth->getContratoId(),
                'socio_relacion_id' => $sociosDirectos->id,
                'timestamp' => Carbon::now()->toISOString()
            ]);
            $invitation->save();

            $this->logger->info('Invitación aceptada exitosamente', [
                'invitation_id' => $invitation->id,
                'contrato_solicitante' => $invitation->contrato_id,
                'contrato_solicitado' => $this->auth->getContratoId(),
                'socio_relacion_id' => $sociosDirectos->id
            ]);

            return $this->respondWithData([
                'success' => true,
                'message' => 'Invitación aceptada exitosamente. Ahora son socios directos.',
                'invitation_id' => $invitation->id,
                'socio_relacion_id' => $sociosDirectos->id
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error al aceptar invitación', [
                'invitation_id' => $invitation->id,
                'contrato_id' => $this->auth->getContratoId(),
                'error' => $e->getMessage()
            ]);

            return $this->respondWithData([
                'success' => false,
                'message' => 'Error al procesar la invitación. Por favor, inténtalo de nuevo.'
            ], 500);
        }
    }
}
