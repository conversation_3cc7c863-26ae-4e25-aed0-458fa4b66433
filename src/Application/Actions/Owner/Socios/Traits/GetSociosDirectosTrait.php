<?php

namespace App\Application\Actions\Owner\Socios\Traits;

use App\Infrastructure\Models\SISociosDirectos;
use Illuminate\Support\Collection;

trait GetSociosDirectosTrait
{
    private function getSociosDirectos(): Collection
    {
        /*
select `bolsa_inmobiliaria`.`contrato_solicitante` as `solicitante`, `bolsa_inmobiliaria`.`contrato_solicitado` as `solicitado`, `bolsa_inmobiliaria`.`authorized_at`, `c`.`numero`, `u`.`id`, `u`.`usuario`, `u`.`name`, `u`.`empresa`, `u`.`ciudad`, `u`.`email`, `u`.`telefono` from `bolsa_inmobiliaria`
inner join `publiweb`.`contratos` as `c` on
    (`c`.`numero` = `bolsa_inmobiliaria`.`contrato_solicitante` and `bolsa_inmobiliaria`.`contrato_solicitante` != ?) or (`c`.`numero` = `bolsa_inmobiliaria`.`contrato_solicitado` and `bolsa_inmobiliaria`.`contrato_solicitado` != ?)
inner join `publiweb`.`clientes` as `u` on `u`.`usuario` = `c`.`usuario`
where (`contrato_solicitado` = ? or `contrato_solicitante` = ?) and `c`.`status` = ?
order by `authorized_at` asc, `u`.`name` asc
         */
        $query = SISociosDirectos::join('publiweb.contratos as c', function ($join) {
            $join->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->on('c.numero', '=', 'bolsa_inmobiliaria.contrato_solicitante')
                        ->where('bolsa_inmobiliaria.contrato_solicitante', '!=', $this->auth->getContratoId());
                })->orWhere(function ($subQuery) {
                    $subQuery->on('c.numero', '=', 'bolsa_inmobiliaria.contrato_solicitado')
                        ->where('bolsa_inmobiliaria.contrato_solicitado', '!=', $this->auth->getContratoId());
                });
            });
        })
            ->join('publiweb.clientes as u', 'u.usuario', '=', 'c.usuario')
            ->where(function ($query) {
                $query->where('contrato_solicitado', $this->auth->getContratoId())
                    ->orWhere('contrato_solicitante', $this->auth->getContratoId());
            })
            ->where('u.activo', 'Si')
            ->where('c.status', 1)
            ->orderBy('authorized_at', 'ASC')
            ->orderBy('u.name', 'ASC')
            ->select(
                'bolsa_inmobiliaria.contrato_solicitante as solicitante',
                'bolsa_inmobiliaria.contrato_solicitado as solicitado',
                'bolsa_inmobiliaria.authorized_at',
                'c.numero',
                'u.id',
                'u.usuario',
                'u.name',
                'u.empresa',
                'u.ciudad',
                'u.email',
                'u.telefono'
            );

        return $query->get();
    }
}
