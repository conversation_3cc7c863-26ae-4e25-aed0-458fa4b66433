<?php

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Infrastructure\Models\SIInvitation;
use Psr\Http\Message\ResponseInterface as Response;

class ListInvitacionesOwnerAction extends OwnerAction
{
    protected function action(): Response
    {
        // Obtener parámetros de consulta
        $params = $this->request->getQueryParams();
        $status = $params['status'] ?? null;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 50;
        $offset = isset($params['offset']) ? (int)$params['offset'] : 0;

        // Construir query
        $query = SIInvitation::where('contrato_id', $this->auth->getContratoId());

        // Filtrar por status si se especifica
        if ($status !== null) {
            $query->where('status', (int)$status);
        }

        // Ordenar por fecha de creación (más recientes primero)
        $query->orderBy('created_at', 'desc');

        // Aplicar paginación
        $total = $query->count();
        $invitations = $query->offset($offset)->limit($limit)->get();

        // Formatear respuesta
        $formattedInvitations = $invitations->map(function ($invitation) {
            return [
                'id' => $invitation->id,
                'email' => $invitation->email,
                'status' => $invitation->status,
                'status_name' => $invitation->status_name,
                'token' => $invitation->token,
                'created_at' => $invitation->created_at->toISOString(),
                'updated_at' => $invitation->updated_at?->toISOString(),
                'bitacora' => $invitation->bitacora
            ];
        });

        return $this->respondWithData([
            'invitations' => $formattedInvitations,
            'pagination' => [
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $total
            ]
        ]);
    }
}
