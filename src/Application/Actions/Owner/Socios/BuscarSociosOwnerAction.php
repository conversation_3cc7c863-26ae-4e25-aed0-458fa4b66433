<?php

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Application\Actions\Owner\Socios\Traits\GetSociosDirectosTrait;
use App\Infrastructure\Models\SIConfig;
use Psr\Http\Message\ResponseInterface as Response;

class BuscarSociosOwnerAction extends OwnerAction
{
    use GetSociosDirectosTrait;

    /**
     * "action"
     *
     * Recuperamos los match con usuarios que tienen contratos activos
     * y posteriormente excluiremos los usuarios que ya son socios directos
     *
     * @return Response
     */
    protected function action(): Response
    {
        // /owner/socios/buscar?q=ricardo
        $search = $this->request->getQueryParams()['q'] ?? '';
        if (!$search) {
            return $this->respondWithData([]);
        }

        // Separamos los términos de búsqueda
        $search = strtolower($search);
        $search = str_replace(' ', '%', $search);

        $socios_directos = $this->getSociosDirectos();
        // Agrego a los socios directos el owner autenticado
        $socios_directos->push([
            'numero' => $this->auth->getContratoId()
        ]);

        $users = SIConfig::join('publiweb.contratos as c', 'c.numero', '=', 'config.contrato')
            ->join('publiweb.clientes as u', 'u.usuario', '=', 'c.usuario')
            ->whereNotIn('c.numero', $socios_directos->pluck('numero'))
            ->where('u.activo', 'Si')
            ->where('c.status', 1)
            ->where('u.pabuscarsocio', 'LIKE', "%$search%")
            ->select(
                'c.numero as contrato',
                'u.id',
                'u.usuario',
                'u.name as nombre',
                'u.empresa',
                $this->db->raw("CONCAT_WS(', ', u.ciudad, u.estado) as ubicacion"),
                'u.ciudad',
                'u.email',
                'u.telefono'
            )
            ->get();

        return $this->respondWithData($users->toArray());
    }
}