<?php

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Application\Services\EmailService;
use App\Infrastructure\Models\SIConfig;
use App\Infrastructure\Models\SISociosDirectos;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpBadRequestException;

class SolicitarSocioOwnerAction extends OwnerAction
{
    protected function action(): Response
    {
        $socio_id = (int)$this->resolveArg('id');

        // Primero verifico si no hay ya sociedad directa
        $solicitud = SISociosDirectos::where(function ($query) use ($socio_id) {
            $query->where('contrato_solicitado', $this->auth->getContratoId())
                ->where('contrato_solicitante', $socio_id);
        })->orWhere(function ($query) use ($socio_id) {
            $query->where('contrato_solicitante', $this->auth->getContratoId())
                ->where('contrato_solicitado', $socio_id);
        })->first();
        if ($solicitud) {
            return $this->respondWithData(['message' => 'Ya son socios directos'], 400);
        }

        try {
            $solicitud = SISociosDirectos::create([
                'contrato_solicitante' => $this->auth->getContratoId(),
                'contrato_solicitado' => $socio_id,
                'bak_solicitante' => $this->auth->getContratoId(),
                'bak_solicitado' => $socio_id,
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error al crear solicitud de socio: ' . $e->getMessage());
            throw new HttpBadRequestException(
                $this->request,
                'Error al crear solicitud de socio: ' . $e->getMessage()
            );
        }

        if (!$solicitud) {
            return $this->respondWithData(['message' => 'No se pudo crear la solicitud'], 500);
        }

        return $this->respondWithData([
            'contrato_id' => $socio_id,
            'message' => 'Solicitud creada'
        ]);
    }
}
