<?php

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Infrastructure\Models\SISociosDirectos;
use Carbon\Carbon;
use Psr\Http\Message\ResponseInterface as Response;

class EliminarSocioOwnerAction extends OwnerAction
{
    protected function action(): Response
    {
        $socio_id = (int)$this->resolveArg('id');
        /*
SELECT id FROM bolsa_inmobiliaria
WHERE (
        (contrato_solicitado = {thisContrato} AND contrato_solicitante = {socioId}) OR
        (contrato_solicitante = {thisContrato} AND contrato_solicitado = {socioId})
    ) AND authorized_at IS NOT NULL
         */
        $solicitud = SISociosDirectos::where(function ($query) use ($socio_id) {
            $query->where(function ($query) use ($socio_id) {
                $query->where('contrato_solicitado', $this->auth->getContratoId())
                    ->where('contrato_solicitante', $socio_id);
            })->orWhere(function ($query) use ($socio_id) {
                $query->where('contrato_solicitante', $this->auth->getContratoId())
                    ->where('contrato_solicitado', $socio_id);
            });
        })
            ->whereNotNull('authorized_at')
            ->first();
        if (!$solicitud) {
            return $this->respondWithData(['message' => 'Solicitud no encontrada'], 404);
        }
        $solicitud->contrato_solicitante = null;
        $solicitud->contrato_solicitado = null;
        $solicitud->deleted_at = Carbon::now();
        $solicitud->save();

        return $this->respondWithData(['message' => 'Solicitud cancelada']);
    }
}
