<?php

declare(strict_types=1);

namespace App\Application\Actions\Owner\Socios;

use App\Application\Actions\Owner\OwnerAction;
use App\Application\Actions\Owner\Socios\Traits\GetSociosDirectosTrait;
use Psr\Http\Message\ResponseInterface as Response;

class ListSociosOwnerAction extends OwnerAction
{
    use GetSociosDirectosTrait;

    protected function action(): Response
    {
        $socios = $this->getSociosDirectos();

        $response = [];
        foreach ($socios as $socio) {
            // Defino el tipo de socio
            if (is_null($socio->authorized_at)) {
                if ($socio->solicitante == $this->auth->getContratoId()) {
                    $tipo = 'pendiente';
                } else {
                    $tipo = 'porAutorizar';
                }
            } else {
                $tipo = 'directo';
            }

            // Respuesta a adicionar al resultado
            $response[] = [
                'id' => $socio->id,
                'usuario' => $socio->usuario,
                'contrato' => $socio->numero,
                'nombre' => $socio->name,
                'empresa' => $socio->empresa,
                'ubicacion' => $socio->ciudad,
                'email' => $socio->email,
                'telefono' => $tel = preg_replace('/[^0-9]/', '', $socio->telefono),
                'wa' => '52' . $tel,
                'tipo' => $tipo,
                'desde' => $socio->authorized_at,
            ];
        }

        return $this->respondWithData($response);
    }
}
