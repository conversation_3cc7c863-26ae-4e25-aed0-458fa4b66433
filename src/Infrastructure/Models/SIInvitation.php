<?php

declare(strict_types=1);

namespace App\Infrastructure\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $contrato_id
 * @property string $email
 * @property int $status
 * @property string $token
 * @property array $bitacora
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class SIInvitation extends Model
{
    protected $table = 'invitations';

    protected $fillable = [
        'contrato_id',
        'email',
        'status',
        'token',
        'bitacora'
    ];

    protected $casts = [
        'bitacora' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => 'integer',
        'contrato_id' => 'integer'
    ];

    // Estados de la invitación
    const STATUS_PENDING = 1;      // Pendiente
    const STATUS_ACCEPTED = 2;     // Aceptada
    const STATUS_EXPIRED = 3;      // Expirada
    const STATUS_CANCELLED = 4;    // Cancelada

    /**
     * Genera un token único para la invitación
     */
    public static function generateToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Agrega una entrada a la bitácora
     */
    public function addToBitacora(array $data): void
    {
        $bitacora = $this->bitacora ?? [];
        $timestamp = Carbon::now()->toISOString();
        $bitacora[$timestamp] = $data;
        $this->bitacora = $bitacora;
    }

    /**
     * Verifica si la invitación está pendiente
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Verifica si la invitación ha sido aceptada
     */
    public function isAccepted(): bool
    {
        return $this->status === self::STATUS_ACCEPTED;
    }

    /**
     * Verifica si la invitación ha expirado
     */
    public function isExpired(): bool
    {
        return $this->status === self::STATUS_EXPIRED;
    }

    /**
     * Marca la invitación como aceptada
     */
    public function markAsAccepted(): void
    {
        $this->status = self::STATUS_ACCEPTED;
        $this->addToBitacora([
            'action' => 'accepted',
            'timestamp' => Carbon::now()->toISOString()
        ]);
        $this->save();
    }

    /**
     * Marca la invitación como expirada
     */
    public function markAsExpired(): void
    {
        $this->status = self::STATUS_EXPIRED;
        $this->addToBitacora([
            'action' => 'expired',
            'timestamp' => Carbon::now()->toISOString()
        ]);
        $this->save();
    }

    /**
     * Marca la invitación como cancelada
     */
    public function markAsCancelled(): void
    {
        $this->status = self::STATUS_CANCELLED;
        $this->addToBitacora([
            'action' => 'cancelled',
            'timestamp' => Carbon::now()->toISOString()
        ]);
        $this->save();
    }

    /**
     * Busca una invitación por token
     */
    public static function findByToken(string $token): ?self
    {
        return self::where('token', $token)->first();
    }

    /**
     * Busca invitaciones pendientes por email
     */
    public static function findPendingByEmail(string $email): ?self
    {
        return self::where('email', $email)
            ->where('status', self::STATUS_PENDING)
            ->first();
    }

    /**
     * Busca invitaciones por contrato
     */
    public static function findByContrato(int $contratoId)
    {
        return self::where('contrato_id', $contratoId)->get();
    }

    /**
     * Obtiene el nombre del estado
     */
    public function getStatusNameAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'Pendiente',
            self::STATUS_ACCEPTED => 'Aceptada',
            self::STATUS_EXPIRED => 'Expirada',
            self::STATUS_CANCELLED => 'Cancelada',
            default => 'Desconocido'
        };
    }
}
